from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from api import v1
from core.logging import init_logging


init_logging()
app = FastAPI(
  title="PayKKa Duty",
  docs_url="/api/v1/docs",
  openapi_url="/api/v1/openapi.json",
)
app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"],
)
app.include_router(v1.router, prefix="/api/v1")

if __name__ == "__main__":
  import uvicorn

  uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_config=None)
