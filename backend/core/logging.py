import logging
import sys

from loguru import logger


class InterceptHandler(logging.Handler):
  """
  日志拦截处理器：将所有 Python 标准日志重定向到 Loguru （用于处理uvicorn / fastapi 等自带的日志）

  工作原理：
  1. 继承自 logging.Handler
  2. 重写 emit 方法处理日志记录
  3. 将标准库日志转换为 Loguru 格式
  """

  def emit(self, record: logging.LogRecord) -> None:
    # 尝试获取日志级别名称
    try:
      level = logger.level(record.levelname).name
    except ValueError:
      level = record.levelno
    # 获取调用帧信息
    frame, depth = logging.currentframe(), 2
    while frame and frame.f_code.co_filename == logging.__file__:
      frame = frame.f_back
      depth += 1

    # 使用 Loguru 记录日志
    logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


def init_logging():
  """
  配置日志系统

  功能：
  1. 控制台彩色输出
  2. 文件日志轮转
  3. 错误日志单独存储
  4. 异步日志记录
  """
  # 步骤1：移除默认处理器
  loggers = (logging.getLogger(name) for name in logging.root.manager.loggerDict)
  for _logger in loggers:
    _logger.handlers = []
    _logger.setLevel(logging.INFO)
    _logger.addHandler(InterceptHandler())

  logger.configure(handlers=[{"sink": sys.stdout, "level": logging.DEBUG}])
