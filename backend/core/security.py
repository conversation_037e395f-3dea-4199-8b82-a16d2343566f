from datetime import datetime, timedelta, UTC

import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from core.settings import get_settings


settings = get_settings()
SECRET_KEY = settings.secret_key
ALGORITHM = "HS256"


def create_access_token(data: dict):
  to_encode = data.copy()
  exp = datetime.now(tz=UTC) + timedelta(days=14)
  to_encode.update({"exp": exp})
  return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


def verify_access_token(token: str):
  try:
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    return payload
  except jwt.InvalidTokenError:
    raise HTTPException(
      status_code=status.HTTP_401_UNAUTHORIZED,
      detail="Invalid token",
      headers={"WWW-Authenticate": "Bearer"},
    )
