from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordBearer
from sqlalchemy.orm import Session

from core.security import verify_access_token
from core.db import get_db
from services.user import user_service
from models.user import User


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


def get_current_user(
  token: str = Depends(oauth2_scheme), session: Session = Depends(get_db)
):
  payload = verify_access_token(token)
  if userid := payload.get("sub"):
    return user_service.get_user_by_userid(session, userid)
  raise HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Not authenticated",
    headers={"WWW-Authenticate": "Bearer"},
  )


SessionDep = Annotated[Session, Depends(get_db)]
AuthDep = Annotated[User, Depends(get_current_user)]
