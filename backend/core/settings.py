from functools import lru_cache

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
  model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

  database_connection_url: str = Field(
    default="sqlite:///./test.db", description="数据库链接"
  )
  secret_key: str = Field(
    default="pngXz15zKmMufmbruYfmtDlt9pVuIGqD", description="密钥"
  )
  wecom_corp_id: str = Field(
    default="ww98e6f25c9311c20b", description="企业微信组织 ID"
  )
  wecom_corp_secret: str = Field(
    default="8hJ3FUQuZUfE2xeHjIJnRsWiThxKwwVgIM-2L8cJUE8",
    description="企业微信自建应用 Secret",
  )
  wecom_agent_id: str = Field(default="1000002", description="企业微信自建应用 ID")
  keep_api_url: str = Field(
    default="http://localhost:8080", description="Keep 平台 API 链接"
  )
  keep_api_key: str = Field(default="mysecretkey", description="Keep 平台 API 密钥")


@lru_cache
def get_settings():
  return Settings()
