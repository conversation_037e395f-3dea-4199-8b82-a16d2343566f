from datetime import datetime, UTC
from random import choice
import string

from sqlalchemy.orm import Session

from models import User
from schemas import UserCreateSchema, UserUpdateSchema


class UserService:
  def create(self, session: Session, payload: UserCreateSchema) -> User:
    user = User(**payload.model_dump())
    user.save(session)
    return user

  def get_user_by_userid(self, session: Session, userid: str) -> User | None:
    return session.query(User).filter(User.userid == userid).first()

  def get_user_by_email(self, session: Session, email: str) -> User | None:
    return session.query(User).filter(User.email == email).first()

  def update(self, session: Session, user: User, payload: UserUpdateSchema) -> User:
    for key, val in vars(payload).items():
      setattr(user, key, val) if val else None
      user.updated_at = datetime.now(tz=UTC)
    user.save(session)
    return user

  def update_last_login_at(self, session: Session, user: User) -> User:
    return self.update(
      session, user, UserUpdateSchema(last_login_at=datetime.now(tz=UTC))
    )

  @classmethod
  def gen_random_password(cls, length: int = 8) -> str:
    return "".join(choice(string.ascii_letters + string.digits) for _ in range(length))


user_service = UserService()
