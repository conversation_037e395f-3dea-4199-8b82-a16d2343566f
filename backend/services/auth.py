from datetime import datetime, UTC, timedelta
from dataclasses import dataclass

import jwt
from fastapi import status
from fastapi.responses import JSONResponse
from fastapi.exceptions import HTTPException
from sqlalchemy.orm import Session
from loguru import logger

from models.user import User
from services.user import user_service
from services.wecom import wecom_service
from services.keep import keep_service
from schemas import (
  UserUpdateSchema,
  UserCreateSchema,
  LoginSchema,
  EmailPasswordLoginSchema,
  WecomLoginSchema,
  AccessTokenSchema,
)
from core.settings import get_settings
from core.security import create_access_token
from utils.crypto import Crypto


settings = get_settings()
crypto = Crypto(settings.secret_key)


@dataclass
class AuthService:
  def login(self, session: Session, payload: LoginSchema) -> JSONResponse:
    match payload:
      case EmailPasswordLoginSchema():
        if user := self._login_with_email_password(payload, session):
          user_service.update_last_login_at(session, user)
          return JSONResponse(
            content={"token": create_access_token({"sub": user.userid})}
          )
        raise HTTPException(
          status_code=status.HTTP_401_UNAUTHORIZED, detail="邮箱或密码不正确"
        )
      case WecomLoginSchema():
        if user := self._login_with_wecom(session, payload):
          user_service.update_last_login_at(session, user)
          return JSONResponse(
            content={"token": create_access_token({"sub": user.userid})}
          )
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="登录失败")
      case _:
        raise HTTPException(
          status_code=status.HTTP_400_BAD_REQUEST, detail="不支持的登录方式"
        )

  def _login_with_email_password(
    self, payload: EmailPasswordLoginSchema, session: Session
  ) -> User | None:
    email = payload.email
    password = payload.password
    if user := user_service.get_user_by_email(session, email):
      if crypto.verify(password, user.encrypted_password):
        return user
    return None

  def _login_with_wecom(
    self,
    session: Session,
    payload: WecomLoginSchema,
  ) -> User | None:
    code = payload.code
    wecom_user_data = wecom_service.get_user(code)
    if user := user_service.get_user_by_userid(
      session, wecom_user_data.get("userid", "")
    ):
      user_update_payload = UserUpdateSchema(**wecom_user_data)
      return user_service.update(session, user, user_update_payload)
    else:
      password = user_service.gen_random_password()
      user_create_payload = UserCreateSchema(
        **wecom_user_data, encrypted_password=crypto.encrypt(password)
      )
      user = user_service.create(session, user_create_payload)
      keep_service.create_user(user.name, password)
      logger.debug(f"Created {user.name}: {password}")
      return user

  def reset_password(self, session: Session, user: User, password: str) -> User:
    keep_service.delete_user(username=user.name)
    keep_service.create_user(user.name, password)
    user.encrypted_password = crypto.encrypt(password)
    user.save(session)
    return user


auth_service = AuthService()
