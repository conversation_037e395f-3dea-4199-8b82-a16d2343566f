from fastapi import APIRouter

from services.keep import keep_service


router = APIRouter()


@router.get("", description="获取所有故障")
def get_all_incidents(
  limit: int = 25,
  offset: int = 0,
  status: str | None = None,
  severity: str | None = None,
  sorting: str | None = "-creation_time",
):
  return keep_service.get_incidents(
    limit=limit, offset=offset, severity=severity, sorting=sorting
  )


@router.get("/{incident_id}", description="获取故障详情")
def get_incident(incident_id: str):
  return keep_service.get_incident(incident_id)
