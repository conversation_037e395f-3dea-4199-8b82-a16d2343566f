from fastapi import APIRouter, status, Depends
from fastapi.responses import PlainTextResponse

from core.deps import SessionDep, AuthDep, get_current_user
from services.wecom import wecom_service
from services.auth import auth_service
from schemas import LoginSchema, UserPublicSchema, ResetPasswordSchema


router = APIRouter()


@router.post("/login", status_code=status.HTTP_200_OK)
def login(payload: LoginSchema, session: SessionDep):
  return auth_service.login(session, payload)


@router.post("/logout", dependencies=[Depends(get_current_user)])
def logout():
  return {"message": "logout"}


@router.get("/session", response_model=UserPublicSchema)
def session(current_user: AuthDep):
  return current_user


@router.get("/wecom/authorize_url", response_class=PlainTextResponse)
def wecom_authorize_url(redirect_url: str):
  return wecom_service.authorize_url(redirect_url)


@router.get("/wecom/sso_url", response_class=PlainTextResponse)
def wecom_sso_url(redirect_url: str):
  return wecom_service.sso_url(redirect_url)


@router.post("/reset-password")
def reset_password(payload: ResetPasswordSchema, user: AuthDep, session: SessionDep):
  return auth_service.reset_password(session, user, payload.password)
