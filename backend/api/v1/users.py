from fastapi import APIRouter, Depends

from core.deps import AuthDep, SessionDep, get_current_user
from services.user import user_service

router = APIRouter(dependencies=[Depends(get_current_user)])


@router.get("/session")
def me(current_user: AuthDep):
  return current_user


@router.get("/{userid}")
def get_user_by_userid(userid: str, session: SessionDep):
  return user_service.get_user_by_userid(session, userid)
