from dataclasses import dataclass

import requests
from loguru import logger


@dataclass
class KeepService:
  api_url: str
  api_key: str
  _http = requests.Session()

  def create_user(self, username: str, password: str):
    return self._post("auth/users", json={"username": username, "passwod": password})

  def delete_user(self, username: str):
    return self._delete(f"auth/users/{username}")

  def get_incident(self, incident_id: str):
    return self._get(f"incidents/{incident_id}")

  def get_incidents(self, limit: int = 25, offset: int = 0):
    return self._get("incidents", params={"limit": limit, "offset": offset})

  def get_incident_alerts(self, incident_id: str, limit: int = 25, offset: int = 0):
    return self._get(
      f"incidents/{incident_id}/alerts", params={"limit": limit, "offset": offset}
    )

  def _put(self, url: str, **kwargs):
    return self._request("PUT", url, **kwargs)

  def _delete(self, url: str, **kwargs):
    return self._request("DELETE", url, **kwargs)

  def _get(self, url: str, **kwargs):
    return self._request("GET", url, **kwargs)

  def _post(self, url: str, **kwargs):
    return self._request("POST", url, **kwargs)

  def _request(self, method: str, url: str, **kwargs):
    kwargs.setdefault(
      "headers", {"X-API-KEY": self.api_key, "Content-Type": "application/json"}
    )
    res = self._http.request(method, f"{self.api_url}/{url}", **kwargs)
    try:
      res.raise_for_status()
    except requests.exceptions.HTTPError as e:
      logger.error(f"请求失败: {e}")
      raise e
    return res.json()


if __name__ == "__main__":
  keep_service = KeepService(api_url="http://localhost:8080", api_key="mysecretkey")
  print(keep_service.get_incidents())
