import hashlib
import base64
from dataclasses import dataclass

from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad


@dataclass
class Crypto:
  secret_key: str

  def encrypt(self, data: str) -> str:
    iv = get_random_bytes(16)
    cipher = AES.new(
      hashlib.sha256(self.secret_key.encode()).digest(), AES.MODE_CBC, iv
    )
    encrypted = cipher.encrypt(pad(data.encode(), AES.block_size))
    return base64.b64encode(iv + encrypted).decode()

  def decrypt(self, data: str) -> str:
    encrypted_bytes = base64.b64decode(data)
    iv = encrypted_bytes[:16]
    encrypted_data = encrypted_bytes[16:]
    cipher = AES.new(
      hashlib.sha256(self.secret_key.encode()).digest(), AES.MODE_CBC, iv
    )
    return unpad(cipher.decrypt(encrypted_data), AES.block_size).decode()

  def verify(self, plain_text: str, encrypted_text: str) -> bool:
    return self.decrypt(encrypted_text) == plain_text
