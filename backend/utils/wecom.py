from dataclasses import dataclass
import urllib.parse

import requests
from loguru import logger


INVALID_ACCESS_TOKEN = 40014
EXPIRED_ACCESS_TOKEN = 42001


@dataclass
class WecomService:
  corp_id: str
  corp_secret: str
  agent_id: str
  access_token: str | None = None
  base_url: str = "https://qyapi.weixin.qq.com/cgi-bin"
  _http = requests.Session()

  def __post_init__(self):
    self._fetch_access_token()

  def authorize_url(self, redirect_url: str, state: str = "LOGIN") -> str:
    return f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={self.corp_id}&agentid={self.agent_id}&redirect_uri={urllib.parse.quote(redirect_url)}&state={state}&response_type=code&scope=snsapi_privateinfo#wechat_redirect"

  def sso_url(self, redirect_url: str, state: str = "LOGIN") -> str:
    return f"https://login.work.weixin.qq.com/wwlogin/sso/login?login_type=CorpApp&agentid={self.agent_id}&appid={self.corp_id}&redirect_uri={urllib.parse.quote(redirect_url)}&state={state}"

  def get_user(self, code: str):
    user_info = self.get_user_info(code)
    if user_ticket := user_info.get("user_ticket"):
      user_detail = self.get_user_detail(user_ticket)
      return user_info | user_detail
    return user_info

  def get_user_info(self, code: str):
    """
    @docs: https://developer.work.weixin.qq.com/document/path/91039
    """
    return self._get("auth/getuserinfo", params={"code": code})

  def get_user_detail(self, user_ticket: str):
    """
    @docs: https://developer.work.weixin.qq.com/document/path/95833
    """
    return self._post("auth/getuserdetail", json={"user_ticket": user_ticket})

  def _get(self, url, **kwargs):
    return self._request("GET", url, **kwargs)

  def _post(self, url, **kwargs):
    return self._request("POST", url, **kwargs)

  def _request(self, method: str, url: str, **kwargs):
    kwargs.setdefault("params", {})["access_token"] = self.access_token
    res = self._http.request(method, f"{self.base_url}/{url}", **kwargs)
    try:
      res.raise_for_status()
    except requests.exceptions.HTTPError as e:
      logger.error(f"请求失败: {e}")
      raise e
    return self._handle_result(res, method, url, **kwargs)

  def _handle_result(self, res: requests.Response, method: str, url: str, **kwargs):
    result = res.json()
    if errcode := result.get("errcode"):
      if errcode in [INVALID_ACCESS_TOKEN, EXPIRED_ACCESS_TOKEN]:
        self._fetch_access_token()
        return self._request(method, url, **kwargs)
      raise ValueError(f"Err Code: {result['errcode']} {result['errmsg']}")
    return result

  def _fetch_access_token(self):
    """
    @docs: https://developer.work.weixin.qq.com/document/path/91039
    """
    logger.info("获取 Access Token")
    res = self._http.get(
      f"{self.base_url}/gettoken",
      params={"corpid": self.corp_id, "corpsecret": self.corp_secret},
    )
    try:
      res.raise_for_status()
    except requests.exceptions.HTTPError as e:
      logger.error(f"获取 Access Token 失败: {e}")
      raise e
    self.access_token = res.json()["access_token"]


if __name__ == "__main__":
  # 测试企业
  wecom = WecomService(
    corp_id="ww98e6f25c9311c20b",
    corp_secret="8hJ3FUQuZUfE2xeHjIJnRsWiThxKwwVgIM-2L8cJUE8",
    agent_id="1000002",
  )
  print(wecom.get_user("Ce8YnHG4yHbNsSE5d6jKwO8O4l5WSStfC65K_oo6Fcs"))
