from enum import StrEnum
from pydantic import BaseModel, Field


class AccessTokenSchema(BaseModel):
  userid: str


class LoginType(StrEnum):
  EMAIL_PASSWORD = "email_password"
  WECOM = "wecom"


class EmailPasswordLoginSchema(BaseModel):
  login_type: LoginType = LoginType.EMAIL_PASSWORD
  email: str = Field(description="邮箱")
  password: str = Field(description="密码")


class WecomLoginSchema(BaseModel):
  login_type: LoginType = LoginType.WECOM
  code: str
  state: str


class ResetPasswordSchema(BaseModel):
  password: str = Field(description="新密码")


LoginSchema = EmailPasswordLoginSchema | WecomLoginSchema
