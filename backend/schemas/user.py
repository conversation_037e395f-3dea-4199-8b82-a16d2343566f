from uuid import UUID

from pydantic import Field
from datetime import datetime

from schemas.base import Schema
from models.user import Status, Gender


class UserBaseSchema(Schema):
  userid: str = Field(description="企业微信用户 ID")
  gender: Gender = Field(description="性别")
  name: str = Field(description="姓名")
  status: Status = Field(description="企微用户激活状态")
  alias: str | None = Field(default=None, description="别名")
  email: str | None = Field(default=None, description="邮箱")
  mobile: str | None = Field(default=None, description="手机号")
  position: str | None = Field(default=None, description="职务")
  avatar: str | None = Field(default=None, description="头像")
  last_login_at: datetime | None = Field(default=None, description="最后登录时间")


class UserCreateSchema(UserBaseSchema):
  encrypted_password: str = Field(description="加密后的密码")


class UserUpdateSchema(UserBaseSchema):
  userid: str | None = None
  gender: Gender | None = None
  name: str | None = None
  status: Status | None = None
  alias: str | None = None
  email: str | None = None
  mobile: str | None = None
  position: str | None = None
  avatar: str | None = None
  last_login_at: datetime | None = None


class UserPublicSchema(UserBaseSchema):
  id: UUID
  userid: str
