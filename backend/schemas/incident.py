from datetime import datetime

from pydantic import BaseModel, Field


class IncidentSchema(BaseModel):
  id: str = Field(description="故障 ID")
  name: str = Field(description="故障名称")
  status: str = Field(description="故障状态")
  severity: str = Field(description="故障级别")
  services: list[str] = Field(description="服务")
  assignee: str | None = Field(default=None, description="处理人")
  created_at: datetime = Field(description="创建时间")
  ended_at: datetime | None = Field(default=None, description="结束时间")
  last_seen_at: datetime = Field(description="最后发生时间")
  alerts_count: int = Field(description="告警数量")
  labels: dict | None = Field(default=None, description="标签")


class IncidentsSchema(BaseModel):
  limit: int
  offset: int
  count: int
  items: list[IncidentSchema]
