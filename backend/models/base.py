from uuid import UUID, uuid4
from datetime import datetime, UTC

from sqlalchemy.orm import Mapped, mapped_column, Session
from sqlalchemy.ext.declarative import declared_attr

from core.db import Base


class BaseModel(Base):
  __abstract__ = True

  id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
  created_at: Mapped[datetime] = mapped_column(
    nullable=True, default=datetime.now(tz=UTC)
  )
  updated_at: Mapped[datetime] = mapped_column(
    nullable=True, default=datetime.now(tz=UTC)
  )

  @declared_attr.directive
  def __tablename__(cls) -> str:
    return cls.__name__.lower()

  def save(self, session: Session):
    session.add(self)
    session.commit()
    session.flush(self)
