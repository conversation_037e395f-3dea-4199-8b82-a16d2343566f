"""empty message

Revision ID: e1b5b4840b4b
Revises: 46c9cb98bd02
Create Date: 2025-09-03 23:49:30.422538

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1b5b4840b4b'
down_revision: Union[str, Sequence[str], None] = '46c9cb98bd02'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'department')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('department', sa.VARCHAR(), nullable=True))
    # ### end Alembic commands ###
