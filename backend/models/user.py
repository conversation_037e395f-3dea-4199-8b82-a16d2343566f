from enum import IntEnum
from datetime import datetime

from sqlalchemy import Integer
from sqlalchemy.orm import Mapped, mapped_column

from models.base import BaseModel


class Gender(IntEnum):
  UNKOWN = 0
  MALE = 1
  FEMALE = 2


class Status(IntEnum):
  ACTIVATED = 1  # 已激活（已激活企业微信或已关注微信插件）
  DISABLED = 2  # 已禁用
  INACTIVE = 4  # 未激活（既未激活企业微信又未关注微信插件）
  RESIGNED = 5  # 已退出企业


class User(BaseModel):
  userid: Mapped[str] = mapped_column(nullable=False, unique=True)
  gender: Mapped[Gender] = mapped_column(Integer, nullable=True, default=Gender.UNKOWN)
  name: Mapped[str] = mapped_column(nullable=False)
  status: Mapped[Status] = mapped_column(Integer, nullable=False)
  encrypted_password: Mapped[str] = mapped_column(nullable=False)
  alias: Mapped[str | None] = mapped_column(nullable=True, default=None)
  email: Mapped[str | None] = mapped_column(nullable=True, default=None)
  mobile: Mapped[str | None] = mapped_column(nullable=True, default=None)
  position: Mapped[str | None] = mapped_column(nullable=True, default=None)
  avatar: Mapped[str | None] = mapped_column(nullable=True, default=None)
  last_login_at: Mapped[datetime | None] = mapped_column(nullable=True, default=None)

  def __repr__(self) -> str:
    return f"<User userid={self.userid}, name={self.name}>"
