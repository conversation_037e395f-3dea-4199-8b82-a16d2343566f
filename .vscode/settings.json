{
  "python.defaultInterpreterPath": "${workspaceFolder}/backend/.venv/bin/python",
  "editor.formatOnSave": true,
  "files.exclude": {
    "**/.venv": true,
    "**/__pycache__": true,
    "**/node_modules": true,
    "**/.ruff_cache": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "editor.quickSuggestions": {
    "strings": "on"
  },
  "tailwindCSS.classAttributes": [
    "class",
    "ui"
  ],
  "tailwindCSS.experimental.classRegex": [
    [
      "ui:\\s*{([^)]*)\\s*}",
      "(?:'|\"|`)([^']*)(?:'|\"|`)"
    ]
  ],
  "typescript.tsdk": "./frontend/node_modules/typescript/lib",
  "biome.lsp.bin": "./frontend/node_modules/.bin/biome",
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnPaste": true,
  "emmet.showExpandedAbbreviation": "never",
  "editor.codeActionsOnSave": {
    "source.fixAll.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
}