{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc -b && vite build", "lint": "ultracite check", "fmt": "ultracite fix", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.12", "@tanstack/react-router": "^1.131.35", "@tanstack/react-router-devtools": "^1.131.35", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@biomejs/biome": "2.2.2", "@eslint/js": "^9.33.0", "@tanstack/router-plugin": "^1.131.35", "@types/node": "^24.3.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "ultracite": "5.3.2", "vite": "^7.1.2"}}