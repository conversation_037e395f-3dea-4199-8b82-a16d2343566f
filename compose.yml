services:
  # db:
  #   image: postgres:17.6-alpine
  #   container_name: db
  #   shm_size: 128mb
  #   environment:
  #     POSTGRES_USER: paykka-duty
  #     POSTGRES_PASSWORD: test
  #     POSTGRES_DB: paykka-duty

  # Keep 服务
  keep:
    image: us-central1-docker.pkg.dev/keephq/keep/keep-api:0.47.4
    container_name: keep
    ports:
      - 8080:8080
    environment:
      USE_NGROK: false
      AUTH_TYPE: DB
      SECRET_MANAGER_TYPE: FILE
      SECRET_MANAGER_DIRECTORY: state
      DATABASE_CONNECTION_STRING: sqlite:///db.sqlite3?check_same_thread=False
      KEEP_JWT_SECRET: simplesecret
      KEEP_DEFAULT_USERNAME: keep
      KEEP_DEFAULT_PASSWORD: keep
      KEEP_DEFAULT_API_KEYS: "admin:admin:mysecretkey"
    profiles:
      - dev
