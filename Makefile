.PHONY: help install backend frontend tunnel

.DEFAULT_GOAL := help
PROJECT_NAME := paykka-duty

help: ## Show this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install frontend and backend dependencies
	@echo "📦 Installing dependencies..."
	@cd backend && uv sync --locked
	@cd frontend && pnpm install

migration: ## Run database migration
	@echo "🚀 Starting migration..."
	@cd backend && uv run alembic revision --autogenerate && uv run alembic upgrade head

backend: ## Run backend server in development mode
	@echo "🚀 Starting backend server..."
	@cd backend && uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload

frontend: ## Run frontend in development mode
	@echo "🚀 Starting frontend..."
	@cd frontend && pnpm dev

tunnel: ## Run cloudflared tunnel
	@echo "🚇 Starting cloudflared tunnel..."
	@cloudflared tunnel run --token `cloudflared tunnel token $(PROJECT_NAME)` --protocol http2

keep: ## Run Keep service
	@echo "🚀 Starting Keep service..."
	@docker compose --profile dev up -d keep
